package com.example.batterystatusapp.presentation.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.batterystatusapp.presentation.dashboard.DashboardScreen

/**
 * Main navigation component for the Battery Status App
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BatteryNavigation() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    Scaffold(
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEach { item ->
                    NavigationBarItem(
                        icon = {
                            Icon(
                                imageVector = item.icon,
                                contentDescription = item.title
                            )
                        },
                        label = { Text(item.title) },
                        selected = currentDestination?.hierarchy?.any { it.route == item.route } == true,
                        onClick = {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Dashboard.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.Dashboard.route) {
                DashboardScreen(
                    onNavigateToHistory = {
                        navController.navigate(Screen.History.route)
                    },
                    onNavigateToApps = {
                        navController.navigate(Screen.Apps.route)
                    },
                    onNavigateToSettings = {
                        navController.navigate(Screen.Settings.route)
                    }
                )
            }
            
            composable(Screen.History.route) {
                // TODO: Implement HistoryScreen
                PlaceholderScreen(title = "History")
            }
            
            composable(Screen.Apps.route) {
                // TODO: Implement AppsScreen
                PlaceholderScreen(title = "Apps")
            }
            
            composable(Screen.Settings.route) {
                // TODO: Implement SettingsScreen
                PlaceholderScreen(title = "Settings")
            }
        }
    }
}

/**
 * Temporary placeholder screen for unimplemented screens
 */
@Composable
private fun PlaceholderScreen(title: String) {
    Card(
        modifier = Modifier.padding(16.dp)
    ) {
        Text(
            text = "$title Screen - Coming Soon!",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(32.dp)
        )
    }
}

/**
 * Screen destinations
 */
sealed class Screen(val route: String, val title: String, val icon: ImageVector) {
    object Dashboard : Screen("dashboard", "Dashboard", Icons.Default.Dashboard)
    object History : Screen("history", "History", Icons.Default.History)
    object Apps : Screen("apps", "Apps", Icons.Default.Apps)
    object Settings : Screen("settings", "Settings", Icons.Default.Settings)
}

/**
 * Bottom navigation items
 */
private val bottomNavItems = listOf(
    Screen.Dashboard,
    Screen.History,
    Screen.Apps,
    Screen.Settings
)
