package com.example.batterystatusapp

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import androidx.core.content.PermissionChecker
import com.example.batterystatusapp.presentation.navigation.BatteryNavigation
import com.example.batterystatusapp.presentation.theme.BatteryStatusAppTheme
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * Main activity for the Battery Status App
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        permissions.entries.forEach { (permission, isGranted) ->
            if (isGranted) {
                Timber.d("Permission granted: $permission")
            } else {
                Timber.w("Permission denied: $permission")
                handlePermissionDenied(permission)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Request necessary permissions
        requestPermissions()

        setContent {
            BatteryStatusAppTheme {
                BatteryNavigation()
            }
        }
    }

    /**
     * Request necessary permissions for battery monitoring
     */
    private fun requestPermissions() {
        val permissions = mutableListOf<String>()

        // Add notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PermissionChecker.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.POST_NOTIFICATIONS)
            }
        }

        // Request permissions if needed
        if (permissions.isNotEmpty()) {
            permissionLauncher.launch(permissions.toTypedArray())
        }

        // Request usage stats permission separately (requires special handling)
        requestUsageStatsPermission()
    }

    /**
     * Request usage stats permission for app power consumption data
     */
    private fun requestUsageStatsPermission() {
        if (!hasUsageStatsPermission()) {
            try {
                val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
                intent.data = Uri.parse("package:$packageName")
                startActivity(intent)
            } catch (e: Exception) {
                Timber.e(e, "Failed to open usage stats settings")
                // Fallback to general usage stats settings
                startActivity(Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS))
            }
        }
    }

    /**
     * Check if usage stats permission is granted
     */
    private fun hasUsageStatsPermission(): Boolean {
        return try {
            val appOpsManager = getSystemService(APP_OPS_SERVICE) as android.app.AppOpsManager
            val mode = appOpsManager.checkOpNoThrow(
                android.app.AppOpsManager.OPSTR_GET_USAGE_STATS,
                android.os.Process.myUid(),
                packageName
            )
            mode == android.app.AppOpsManager.MODE_ALLOWED
        } catch (e: Exception) {
            Timber.e(e, "Failed to check usage stats permission")
            false
        }
    }

    /**
     * Handle permission denial
     */
    private fun handlePermissionDenied(permission: String) {
        when (permission) {
            Manifest.permission.POST_NOTIFICATIONS -> {
                Timber.w("Notification permission denied - alerts will not work")
            }
            else -> {
                Timber.w("Permission denied: $permission")
            }
        }
    }
}