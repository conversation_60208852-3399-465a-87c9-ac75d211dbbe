package com.example.batterystatusapp.data.repository

import com.example.batterystatusapp.data.database.BatteryAlertDao
import com.example.batterystatusapp.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for battery alert operations
 */
@Singleton
class BatteryAlertRepository @Inject constructor(
    private val batteryAlertDao: BatteryAlertDao
) {

    /**
     * Get all battery alerts
     */
    fun getAllBatteryAlerts(): Flow<List<BatteryAlert>> {
        return batteryAlertDao.getAllBatteryAlerts().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get enabled battery alerts
     */
    fun getEnabledBatteryAlerts(): Flow<List<BatteryAlert>> {
        return batteryAlertDao.getEnabledBatteryAlerts().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Save battery alert
     */
    suspend fun saveBatteryAlert(batteryAlert: BatteryAlert): Long {
        return try {
            val alertId = batteryAlertDao.insertBatteryAlert(batteryAlert.toEntity())
            Timber.d("Battery alert saved: ${batteryAlert.title}")
            alertId
        } catch (e: Exception) {
            Timber.e(e, "Failed to save battery alert: ${batteryAlert.title}")
            -1L
        }
    }

    /**
     * Update battery alert
     */
    suspend fun updateBatteryAlert(batteryAlert: BatteryAlert) {
        try {
            batteryAlertDao.updateBatteryAlert(batteryAlert.toEntity())
            Timber.d("Battery alert updated: ${batteryAlert.title}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update battery alert: ${batteryAlert.title}")
        }
    }

    /**
     * Delete battery alert
     */
    suspend fun deleteBatteryAlert(alertId: Long) {
        try {
            batteryAlertDao.deleteBatteryAlertById(alertId)
            Timber.d("Battery alert deleted: $alertId")
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete battery alert: $alertId")
        }
    }

    /**
     * Get battery alert by ID
     */
    suspend fun getBatteryAlertById(alertId: Long): BatteryAlert? {
        return try {
            batteryAlertDao.getBatteryAlertById(alertId)?.toDomainModel()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get battery alert by ID: $alertId")
            null
        }
    }

    /**
     * Get battery alerts by type
     */
    fun getBatteryAlertsByType(type: AlertType): Flow<List<BatteryAlert>> {
        return batteryAlertDao.getBatteryAlertsByType(type.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get enabled alerts by type
     */
    suspend fun getEnabledAlertsByType(type: AlertType): List<BatteryAlert> {
        return try {
            batteryAlertDao.getEnabledAlertsByType(type.name).map { it.toDomainModel() }
        } catch (e: Exception) {
            Timber.e(e, "Failed to get enabled alerts by type: ${type.name}")
            emptyList()
        }
    }

    /**
     * Update alert enabled status
     */
    suspend fun updateAlertEnabledStatus(alertId: Long, isEnabled: Boolean) {
        try {
            batteryAlertDao.updateAlertEnabledStatus(alertId, isEnabled)
            Timber.d("Alert enabled status updated: $alertId -> $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update alert enabled status: $alertId")
        }
    }

    /**
     * Mark alert as triggered
     */
    suspend fun markAlertAsTriggered(alertId: Long) {
        try {
            batteryAlertDao.updateLastTriggered(alertId, System.currentTimeMillis())
            Timber.d("Alert marked as triggered: $alertId")
        } catch (e: Exception) {
            Timber.e(e, "Failed to mark alert as triggered: $alertId")
        }
    }

    /**
     * Get alerts that haven't been triggered recently
     */
    suspend fun getAlertsNotTriggeredSince(sinceTime: Long): List<BatteryAlert> {
        return try {
            batteryAlertDao.getAlertsNotTriggeredSince(sinceTime).map { it.toDomainModel() }
        } catch (e: Exception) {
            Timber.e(e, "Failed to get alerts not triggered since: $sinceTime")
            emptyList()
        }
    }

    /**
     * Get recently triggered alerts
     */
    fun getRecentlyTriggeredAlerts(sinceTime: Long): Flow<List<BatteryAlert>> {
        return batteryAlertDao.getRecentlyTriggeredAlerts(sinceTime).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get alerts by priority
     */
    fun getAlertsByPriority(priority: AlertPriority): Flow<List<BatteryAlert>> {
        return batteryAlertDao.getAlertsByPriority(priority.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get enabled alerts count
     */
    suspend fun getEnabledAlertsCount(): Int {
        return try {
            batteryAlertDao.getEnabledAlertsCount()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get enabled alerts count")
            0
        }
    }

    /**
     * Check if alert exists for type and threshold
     */
    suspend fun alertExistsForTypeAndThreshold(type: AlertType, threshold: Int): Boolean {
        return try {
            batteryAlertDao.alertExistsForTypeAndThreshold(type.name, threshold) > 0
        } catch (e: Exception) {
            Timber.e(e, "Failed to check if alert exists")
            false
        }
    }

    /**
     * Initialize default alerts if none exist
     */
    suspend fun initializeDefaultAlerts() {
        try {
            val totalAlerts = batteryAlertDao.getTotalAlertsCount()
            if (totalAlerts == 0) {
                val defaultAlerts = getDefaultAlerts()
                defaultAlerts.forEach { alert ->
                    saveBatteryAlert(alert)
                }
                Timber.d("Default alerts initialized")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize default alerts")
        }
    }

    /**
     * Delete all battery alerts
     */
    suspend fun deleteAllBatteryAlerts() {
        try {
            batteryAlertDao.deleteAllBatteryAlerts()
            Timber.d("All battery alerts deleted")
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete all battery alerts")
        }
    }

    /**
     * Get default battery alerts
     */
    private fun getDefaultAlerts(): List<BatteryAlert> {
        return listOf(
            BatteryAlert(
                type = AlertType.LOW_BATTERY,
                threshold = 20,
                isEnabled = true,
                title = "Low Battery",
                message = "Battery level is below 20%",
                priority = AlertPriority.HIGH
            ),
            BatteryAlert(
                type = AlertType.HIGH_TEMPERATURE,
                threshold = 40,
                isEnabled = true,
                title = "High Temperature",
                message = "Battery temperature is above 40°C",
                priority = AlertPriority.CRITICAL
            ),
            BatteryAlert(
                type = AlertType.CHARGING_COMPLETE,
                threshold = 100,
                isEnabled = true,
                title = "Charging Complete",
                message = "Battery is fully charged",
                priority = AlertPriority.NORMAL
            )
        )
    }
}
