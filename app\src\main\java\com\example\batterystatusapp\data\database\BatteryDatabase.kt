package com.example.batterystatusapp.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.example.batterystatusapp.data.model.*

/**
 * Room database for the Battery Status App
 */
@Database(
    entities = [
        BatteryHistoryEntity::class,
        AppPowerUsageEntity::class,
        BatteryAlertEntity::class,
        UserPreferencesEntity::class
    ],
    version = 1,
    exportSchema = false
)
abstract class BatteryDatabase : RoomDatabase() {

    abstract fun batteryDao(): BatteryDao
    abstract fun appPowerUsageDao(): AppPowerUsageDao
    abstract fun batteryAlertDao(): BatteryAlertDao
    abstract fun userPreferencesDao(): UserPreferencesDao

    companion object {
        @Volatile
        private var INSTANCE: BatteryDatabase? = null

        fun getDatabase(context: Context): BatteryDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    BatteryDatabase::class.java,
                    "battery_database"
                )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }

    /**
     * Database callback for initialization
     */
    private class DatabaseCallback : RoomDatabase.Callback() {
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            // Initialize default data if needed
        }
    }
}

/**
 * Database migrations (for future versions)
 */
val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // Migration logic for version 2
    }
}
