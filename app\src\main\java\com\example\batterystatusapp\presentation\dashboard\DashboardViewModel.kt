package com.example.batterystatusapp.presentation.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.batterystatusapp.data.model.BatteryInfo
import com.example.batterystatusapp.data.model.UserPreferences
import com.example.batterystatusapp.data.repository.BatteryRepository
import com.example.batterystatusapp.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for the Dashboard screen
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val batteryRepository: BatteryRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        observeBatteryInfo()
        observeUserPreferences()
        initializeData()
    }

    /**
     * Observe real-time battery information
     */
    private fun observeBatteryInfo() {
        viewModelScope.launch {
            batteryRepository.getBatteryInfoFlow()
                .catch { exception ->
                    Timber.e(exception, "Error observing battery info")
                    _uiState.update { it.copy(error = exception.message) }
                }
                .collect { batteryInfo ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            batteryInfo = batteryInfo,
                            isLoading = false,
                            error = null
                        )
                    }
                    
                    // Save battery info to database
                    saveBatteryInfo(batteryInfo)
                }
        }
    }

    /**
     * Observe user preferences
     */
    private fun observeUserPreferences() {
        viewModelScope.launch {
            userPreferencesRepository.getUserPreferences()
                .catch { exception ->
                    Timber.e(exception, "Error observing user preferences")
                }
                .collect { preferences ->
                    _uiState.update { it.copy(userPreferences = preferences) }
                }
        }
    }

    /**
     * Initialize data and default preferences
     */
    private fun initializeData() {
        viewModelScope.launch {
            try {
                userPreferencesRepository.initializeDefaultPreferences()
            } catch (e: Exception) {
                Timber.e(e, "Failed to initialize default preferences")
            }
        }
    }

    /**
     * Save battery information to database
     */
    private fun saveBatteryInfo(batteryInfo: BatteryInfo) {
        viewModelScope.launch {
            try {
                batteryRepository.saveBatteryInfo(batteryInfo)
            } catch (e: Exception) {
                Timber.e(e, "Failed to save battery info")
            }
        }
    }

    /**
     * Refresh battery data
     */
    fun refreshData() {
        viewModelScope.launch {
            _uiState.update { it.copy(isRefreshing = true) }
            
            try {
                val currentBatteryInfo = batteryRepository.getCurrentBatteryInfo()
                _uiState.update { currentState ->
                    currentState.copy(
                        batteryInfo = currentBatteryInfo,
                        isRefreshing = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to refresh battery data")
                _uiState.update { 
                    it.copy(
                        isRefreshing = false,
                        error = e.message
                    )
                }
            }
        }
    }

    /**
     * Clear error state
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * Get battery health status color
     */
    fun getBatteryHealthColor(): androidx.compose.ui.graphics.Color {
        return when (_uiState.value.batteryInfo?.health) {
            com.example.batterystatusapp.data.model.BatteryHealth.GOOD -> 
                androidx.compose.ui.graphics.Color.Green
            com.example.batterystatusapp.data.model.BatteryHealth.OVERHEAT,
            com.example.batterystatusapp.data.model.BatteryHealth.OVER_VOLTAGE -> 
                androidx.compose.ui.graphics.Color.Red
            com.example.batterystatusapp.data.model.BatteryHealth.COLD -> 
                androidx.compose.ui.graphics.Color.Blue
            else -> androidx.compose.ui.graphics.Color.Gray
        }
    }

    /**
     * Get battery percentage color based on level
     */
    fun getBatteryPercentageColor(): androidx.compose.ui.graphics.Color {
        val percentage = _uiState.value.batteryInfo?.percentage ?: 0
        return when {
            percentage >= 80 -> androidx.compose.ui.graphics.Color.Green
            percentage >= 50 -> androidx.compose.ui.graphics.Color(0xFFFF9800) // Orange
            percentage >= 20 -> androidx.compose.ui.graphics.Color(0xFFFF5722) // Deep Orange
            else -> androidx.compose.ui.graphics.Color.Red
        }
    }

    /**
     * Format temperature based on user preference
     */
    fun formatTemperature(celsius: Float): String {
        val preferences = _uiState.value.userPreferences
        return when (preferences.temperatureUnit) {
            com.example.batterystatusapp.data.model.TemperatureUnit.CELSIUS -> 
                "${celsius.toInt()}°C"
            com.example.batterystatusapp.data.model.TemperatureUnit.FAHRENHEIT -> 
                "${(celsius * 9/5 + 32).toInt()}°F"
        }
    }
}

/**
 * UI state for the Dashboard screen
 */
data class DashboardUiState(
    val batteryInfo: BatteryInfo? = null,
    val userPreferences: UserPreferences = UserPreferences(),
    val isLoading: Boolean = true,
    val isRefreshing: Boolean = false,
    val error: String? = null
)
