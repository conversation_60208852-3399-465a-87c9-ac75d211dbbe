package com.example.batterystatusapp.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Data class representing power usage by individual apps
 */
data class AppPowerUsage(
    val packageName: String,
    val appName: String,
    val powerUsagePercent: Double,
    val foregroundTime: Long, // in milliseconds
    val backgroundTime: Long, // in milliseconds
    val totalTime: Long, // in milliseconds
    val icon: String? = null // Base64 encoded icon or resource identifier
)

/**
 * Room entity for storing app power usage data
 */
@Entity(tableName = "app_power_usage")
data class AppPowerUsageEntity(
    @PrimaryKey
    val packageName: String,
    val appName: String,
    val powerUsagePercent: Double,
    val foregroundTime: Long,
    val backgroundTime: Long,
    val totalTime: Long,
    val icon: String?,
    val lastUpdated: Long // Unix timestamp
)

/**
 * Extension functions for converting between domain models and entities
 */
fun AppPowerUsage.toEntity(): AppPowerUsageEntity {
    return AppPowerUsageEntity(
        packageName = packageName,
        appName = appName,
        powerUsagePercent = powerUsagePercent,
        foregroundTime = foregroundTime,
        backgroundTime = backgroundTime,
        totalTime = totalTime,
        icon = icon,
        lastUpdated = System.currentTimeMillis()
    )
}

fun AppPowerUsageEntity.toDomainModel(): AppPowerUsage {
    return AppPowerUsage(
        packageName = packageName,
        appName = appName,
        powerUsagePercent = powerUsagePercent,
        foregroundTime = foregroundTime,
        backgroundTime = backgroundTime,
        totalTime = totalTime,
        icon = icon
    )
}
