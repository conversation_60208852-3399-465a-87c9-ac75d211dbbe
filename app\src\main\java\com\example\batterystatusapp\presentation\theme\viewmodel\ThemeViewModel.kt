package com.example.batterystatusapp.presentation.theme.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.batterystatusapp.data.model.UserPreferences
import com.example.batterystatusapp.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for managing app theme and user preferences
 */
@HiltViewModel
class ThemeViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    /**
     * User preferences state flow
     */
    val userPreferences: StateFlow<UserPreferences> = userPreferencesRepository
        .getUserPreferences()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = UserPreferences()
        )

    init {
        initializePreferences()
    }

    /**
     * Initialize default preferences if they don't exist
     */
    private fun initializePreferences() {
        viewModelScope.launch {
            try {
                userPreferencesRepository.initializeDefaultPreferences()
                Timber.d("Theme preferences initialized")
            } catch (e: Exception) {
                Timber.e(e, "Failed to initialize theme preferences")
            }
        }
    }
}
