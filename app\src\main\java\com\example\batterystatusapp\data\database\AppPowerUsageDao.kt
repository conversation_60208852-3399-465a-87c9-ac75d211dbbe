package com.example.batterystatusapp.data.database

import androidx.room.*
import com.example.batterystatusapp.data.model.AppPowerUsageEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for app power usage operations
 */
@Dao
interface AppPowerUsageDao {

    /**
     * Insert or update app power usage data
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAppPowerUsage(appPowerUsage: AppPowerUsageEntity)

    /**
     * Insert or update multiple app power usage entries
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAppPowerUsageList(appPowerUsageList: List<AppPowerUsageEntity>)

    /**
     * Get all app power usage data ordered by power usage percentage descending
     */
    @Query("SELECT * FROM app_power_usage ORDER BY powerUsagePercent DESC")
    fun getAllAppPowerUsage(): Flow<List<AppPowerUsageEntity>>

    /**
     * Get top N apps by power usage
     */
    @Query("SELECT * FROM app_power_usage ORDER BY powerUsagePercent DESC LIMIT :limit")
    fun getTopPowerConsumingApps(limit: Int): Flow<List<AppPowerUsageEntity>>

    /**
     * Get power usage for a specific app
     */
    @Query("SELECT * FROM app_power_usage WHERE packageName = :packageName")
    suspend fun getAppPowerUsage(packageName: String): AppPowerUsageEntity?

    /**
     * Get total power usage percentage
     */
    @Query("SELECT SUM(powerUsagePercent) FROM app_power_usage")
    suspend fun getTotalPowerUsage(): Double?

    /**
     * Get apps with power usage above threshold
     */
    @Query("SELECT * FROM app_power_usage WHERE powerUsagePercent > :threshold ORDER BY powerUsagePercent DESC")
    fun getAppsAboveThreshold(threshold: Double): Flow<List<AppPowerUsageEntity>>

    /**
     * Search apps by name
     */
    @Query("SELECT * FROM app_power_usage WHERE appName LIKE '%' || :searchQuery || '%' ORDER BY powerUsagePercent DESC")
    fun searchAppsByName(searchQuery: String): Flow<List<AppPowerUsageEntity>>

    /**
     * Delete app power usage data for a specific package
     */
    @Query("DELETE FROM app_power_usage WHERE packageName = :packageName")
    suspend fun deleteAppPowerUsage(packageName: String)

    /**
     * Delete all app power usage data
     */
    @Query("DELETE FROM app_power_usage")
    suspend fun deleteAllAppPowerUsage()

    /**
     * Update last updated timestamp for an app
     */
    @Query("UPDATE app_power_usage SET lastUpdated = :timestamp WHERE packageName = :packageName")
    suspend fun updateLastUpdated(packageName: String, timestamp: Long)

    /**
     * Get apps updated before a certain time (for cleanup)
     */
    @Query("SELECT * FROM app_power_usage WHERE lastUpdated < :beforeTime")
    suspend fun getAppsUpdatedBefore(beforeTime: Long): List<AppPowerUsageEntity>

    /**
     * Delete apps updated before a certain time
     */
    @Query("DELETE FROM app_power_usage WHERE lastUpdated < :beforeTime")
    suspend fun deleteAppsUpdatedBefore(beforeTime: Long)

    /**
     * Get count of tracked apps
     */
    @Query("SELECT COUNT(*) FROM app_power_usage")
    suspend fun getTrackedAppsCount(): Int

    /**
     * Get apps with significant background usage
     */
    @Query("""
        SELECT * FROM app_power_usage 
        WHERE backgroundTime > foregroundTime 
        AND powerUsagePercent > :minUsage 
        ORDER BY powerUsagePercent DESC
    """)
    fun getAppsWithHighBackgroundUsage(minUsage: Double = 1.0): Flow<List<AppPowerUsageEntity>>

    /**
     * Get power usage statistics
     */
    @Query("""
        SELECT 
            COUNT(*) as totalApps,
            AVG(powerUsagePercent) as averageUsage,
            MAX(powerUsagePercent) as maxUsage,
            SUM(powerUsagePercent) as totalUsage
        FROM app_power_usage
    """)
    suspend fun getPowerUsageStatistics(): PowerUsageStatistics?
}

/**
 * Data class for power usage statistics
 */
data class PowerUsageStatistics(
    val totalApps: Int,
    val averageUsage: Double,
    val maxUsage: Double,
    val totalUsage: Double
)
