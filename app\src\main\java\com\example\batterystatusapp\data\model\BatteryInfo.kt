package com.example.batterystatusapp.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDateTime

/**
 * Data class representing current battery information
 */
data class BatteryInfo(
    val percentage: Int,
    val health: BatteryHealth,
    val status: BatteryStatus,
    val temperature: Float, // in Celsius
    val voltage: Int, // in millivolts
    val current: Int, // in microamperes
    val capacity: Int, // in microampere-hours
    val technology: String,
    val isCharging: Boolean,
    val pluggedType: PluggedType,
    val chargeTimeRemaining: Long?, // in milliseconds
    val timestamp: LocalDateTime = LocalDateTime.now()
)

/**
 * Room entity for storing battery history
 */
@Entity(tableName = "battery_history")
data class BatteryHistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val percentage: Int,
    val health: String,
    val status: String,
    val temperature: Float,
    val voltage: Int,
    val current: Int,
    val capacity: Int,
    val technology: String,
    val isCharging: Boolean,
    val pluggedType: String,
    val chargeTimeRemaining: Long?,
    val timestamp: Long // Unix timestamp
)

/**
 * Battery health status enumeration
 */
enum class BatteryHealth(val displayName: String) {
    UNKNOWN("Unknown"),
    GOOD("Good"),
    OVERHEAT("Overheat"),
    DEAD("Dead"),
    OVER_VOLTAGE("Over Voltage"),
    UNSPECIFIED_FAILURE("Unspecified Failure"),
    COLD("Cold")
}

/**
 * Battery charging status enumeration
 */
enum class BatteryStatus(val displayName: String) {
    UNKNOWN("Unknown"),
    CHARGING("Charging"),
    DISCHARGING("Discharging"),
    NOT_CHARGING("Not Charging"),
    FULL("Full")
}

/**
 * Power source type enumeration
 */
enum class PluggedType(val displayName: String) {
    NONE("Not Plugged"),
    AC("AC Adapter"),
    USB("USB"),
    WIRELESS("Wireless")
}

/**
 * Extension functions for converting between domain models and entities
 */
fun BatteryInfo.toEntity(): BatteryHistoryEntity {
    return BatteryHistoryEntity(
        percentage = percentage,
        health = health.name,
        status = status.name,
        temperature = temperature,
        voltage = voltage,
        current = current,
        capacity = capacity,
        technology = technology,
        isCharging = isCharging,
        pluggedType = pluggedType.name,
        chargeTimeRemaining = chargeTimeRemaining,
        timestamp = System.currentTimeMillis()
    )
}

fun BatteryHistoryEntity.toDomainModel(): BatteryInfo {
    return BatteryInfo(
        percentage = percentage,
        health = BatteryHealth.valueOf(health),
        status = BatteryStatus.valueOf(status),
        temperature = temperature,
        voltage = voltage,
        current = current,
        capacity = capacity,
        technology = technology,
        isCharging = isCharging,
        pluggedType = PluggedType.valueOf(pluggedType),
        chargeTimeRemaining = chargeTimeRemaining,
        timestamp = LocalDateTime.ofEpochSecond(timestamp / 1000, 0, java.time.ZoneOffset.UTC)
    )
}
