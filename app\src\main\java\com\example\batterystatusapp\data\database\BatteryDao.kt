package com.example.batterystatusapp.data.database

import androidx.room.*
import com.example.batterystatusapp.data.model.BatteryHistoryEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for battery history operations
 */
@Dao
interface BatteryDao {

    /**
     * Insert a new battery history entry
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBatteryHistory(batteryHistory: BatteryHistoryEntity)

    /**
     * Get all battery history entries ordered by timestamp descending
     */
    @Query("SELECT * FROM battery_history ORDER BY timestamp DESC")
    fun getAllBatteryHistory(): Flow<List<BatteryHistoryEntity>>

    /**
     * Get battery history within a specific time range
     */
    @Query("""
        SELECT * FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime 
        ORDER BY timestamp ASC
    """)
    fun getBatteryHistoryInRange(startTime: Long, endTime: Long): Flow<List<BatteryHistoryEntity>>

    /**
     * Get the latest battery history entry
     */
    @Query("SELECT * FROM battery_history ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestBatteryHistory(): BatteryHistoryEntity?

    /**
     * Get battery history for the last N hours
     */
    @Query("""
        SELECT * FROM battery_history 
        WHERE timestamp > :sinceTime 
        ORDER BY timestamp ASC
    """)
    fun getBatteryHistorySince(sinceTime: Long): Flow<List<BatteryHistoryEntity>>

    /**
     * Get average battery percentage for a time period
     */
    @Query("""
        SELECT AVG(percentage) FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime
    """)
    suspend fun getAverageBatteryPercentage(startTime: Long, endTime: Long): Double?

    /**
     * Get maximum temperature recorded in a time period
     */
    @Query("""
        SELECT MAX(temperature) FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime
    """)
    suspend fun getMaxTemperature(startTime: Long, endTime: Long): Float?

    /**
     * Get minimum temperature recorded in a time period
     */
    @Query("""
        SELECT MIN(temperature) FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime
    """)
    suspend fun getMinTemperature(startTime: Long, endTime: Long): Float?

    /**
     * Delete battery history older than specified timestamp
     */
    @Query("DELETE FROM battery_history WHERE timestamp < :olderThan")
    suspend fun deleteOldBatteryHistory(olderThan: Long)

    /**
     * Get count of battery history entries
     */
    @Query("SELECT COUNT(*) FROM battery_history")
    suspend fun getBatteryHistoryCount(): Int

    /**
     * Delete all battery history
     */
    @Query("DELETE FROM battery_history")
    suspend fun deleteAllBatteryHistory()

    /**
     * Get battery percentage data points for charting
     */
    @Query("""
        SELECT timestamp, percentage FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime 
        ORDER BY timestamp ASC
    """)
    fun getBatteryPercentageData(startTime: Long, endTime: Long): Flow<List<BatteryDataPoint>>

    /**
     * Get temperature data points for charting
     */
    @Query("""
        SELECT timestamp, temperature FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime 
        ORDER BY timestamp ASC
    """)
    fun getTemperatureData(startTime: Long, endTime: Long): Flow<List<TemperatureDataPoint>>

    /**
     * Get voltage data points for charting
     */
    @Query("""
        SELECT timestamp, voltage FROM battery_history 
        WHERE timestamp BETWEEN :startTime AND :endTime 
        ORDER BY timestamp ASC
    """)
    fun getVoltageData(startTime: Long, endTime: Long): Flow<List<VoltageDataPoint>>
}

/**
 * Data classes for chart data points
 */
data class BatteryDataPoint(
    val timestamp: Long,
    val percentage: Int
)

data class TemperatureDataPoint(
    val timestamp: Long,
    val temperature: Float
)

data class VoltageDataPoint(
    val timestamp: Long,
    val voltage: Int
)
