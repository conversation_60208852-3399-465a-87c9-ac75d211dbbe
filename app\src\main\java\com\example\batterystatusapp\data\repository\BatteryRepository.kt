package com.example.batterystatusapp.data.repository

import com.example.batterystatusapp.data.database.BatteryDao
import com.example.batterystatusapp.data.database.BatteryDataPoint
import com.example.batterystatusapp.data.database.TemperatureDataPoint
import com.example.batterystatusapp.data.database.VoltageDataPoint
import com.example.batterystatusapp.data.model.BatteryInfo
import com.example.batterystatusapp.data.model.toEntity
import com.example.batterystatusapp.data.model.toDomainModel
import com.example.batterystatusapp.data.source.BatteryDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for battery data operations
 */
@Singleton
class BatteryRepository @Inject constructor(
    private val batteryDataSource: BatteryDataSource,
    private val batteryDao: BatteryDao
) {

    /**
     * Get current battery information from system
     */
    fun getCurrentBatteryInfo(): BatteryInfo {
        return batteryDataSource.getCurrentBatteryInfo()
    }

    /**
     * Get real-time battery information as Flow
     */
    fun getBatteryInfoFlow(): Flow<BatteryInfo> {
        return batteryDataSource.getBatteryInfoFlow()
    }

    /**
     * Save battery information to database
     */
    suspend fun saveBatteryInfo(batteryInfo: BatteryInfo) {
        try {
            batteryDao.insertBatteryHistory(batteryInfo.toEntity())
            Timber.d("Battery info saved: ${batteryInfo.percentage}%")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save battery info")
        }
    }

    /**
     * Get all battery history
     */
    fun getAllBatteryHistory(): Flow<List<BatteryInfo>> {
        return batteryDao.getAllBatteryHistory().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get battery history within time range
     */
    fun getBatteryHistoryInRange(startTime: Long, endTime: Long): Flow<List<BatteryInfo>> {
        return batteryDao.getBatteryHistoryInRange(startTime, endTime).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get battery history since specific time
     */
    fun getBatteryHistorySince(sinceTime: Long): Flow<List<BatteryInfo>> {
        return batteryDao.getBatteryHistorySince(sinceTime).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get latest battery history entry
     */
    suspend fun getLatestBatteryHistory(): BatteryInfo? {
        return try {
            batteryDao.getLatestBatteryHistory()?.toDomainModel()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get latest battery history")
            null
        }
    }

    /**
     * Get battery percentage data for charting
     */
    fun getBatteryPercentageData(startTime: Long, endTime: Long): Flow<List<BatteryDataPoint>> {
        return batteryDao.getBatteryPercentageData(startTime, endTime)
    }

    /**
     * Get temperature data for charting
     */
    fun getTemperatureData(startTime: Long, endTime: Long): Flow<List<TemperatureDataPoint>> {
        return batteryDao.getTemperatureData(startTime, endTime)
    }

    /**
     * Get voltage data for charting
     */
    fun getVoltageData(startTime: Long, endTime: Long): Flow<List<VoltageDataPoint>> {
        return batteryDao.getVoltageData(startTime, endTime)
    }

    /**
     * Get battery statistics for time period
     */
    suspend fun getBatteryStatistics(startTime: Long, endTime: Long): BatteryStatistics? {
        return try {
            val avgPercentage = batteryDao.getAverageBatteryPercentage(startTime, endTime)
            val maxTemp = batteryDao.getMaxTemperature(startTime, endTime)
            val minTemp = batteryDao.getMinTemperature(startTime, endTime)
            
            if (avgPercentage != null && maxTemp != null && minTemp != null) {
                BatteryStatistics(
                    averagePercentage = avgPercentage,
                    maxTemperature = maxTemp,
                    minTemperature = minTemp
                )
            } else null
        } catch (e: Exception) {
            Timber.e(e, "Failed to get battery statistics")
            null
        }
    }

    /**
     * Clean up old battery history
     */
    suspend fun cleanupOldHistory(olderThanDays: Int = 30) {
        try {
            val cutoffTime = System.currentTimeMillis() - (olderThanDays * 24 * 60 * 60 * 1000L)
            batteryDao.deleteOldBatteryHistory(cutoffTime)
            Timber.d("Cleaned up battery history older than $olderThanDays days")
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup old battery history")
        }
    }

    /**
     * Get battery history count
     */
    suspend fun getBatteryHistoryCount(): Int {
        return try {
            batteryDao.getBatteryHistoryCount()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get battery history count")
            0
        }
    }

    /**
     * Delete all battery history
     */
    suspend fun deleteAllBatteryHistory() {
        try {
            batteryDao.deleteAllBatteryHistory()
            Timber.d("All battery history deleted")
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete all battery history")
        }
    }
}

/**
 * Data class for battery statistics
 */
data class BatteryStatistics(
    val averagePercentage: Double,
    val maxTemperature: Float,
    val minTemperature: Float
)
