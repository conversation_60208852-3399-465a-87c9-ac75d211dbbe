package com.example.batterystatusapp.di

import com.example.batterystatusapp.data.database.*
import com.example.batterystatusapp.data.repository.*
import com.example.batterystatusapp.data.source.BatteryDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for repository dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideBatteryRepository(
        batteryDataSource: BatteryDataSource,
        batteryDao: BatteryDao
    ): BatteryRepository {
        return BatteryRepository(batteryDataSource, batteryDao)
    }

    @Provides
    @Singleton
    fun provideUserPreferencesRepository(
        userPreferencesDao: UserPreferencesDao
    ): UserPreferencesRepository {
        return UserPreferencesRepository(userPreferencesDao)
    }

    @Provides
    @Singleton
    fun provideAppPowerUsageRepository(
        appPowerUsageDao: AppPowerUsageDao
    ): AppPowerUsageRepository {
        return AppPowerUsageRepository(appPowerUsageDao)
    }

    @Provides
    @Singleton
    fun provideBatteryAlertRepository(
        batteryAlertDao: BatteryAlertDao
    ): BatteryAlertRepository {
        return BatteryAlertRepository(batteryAlertDao)
    }
}
