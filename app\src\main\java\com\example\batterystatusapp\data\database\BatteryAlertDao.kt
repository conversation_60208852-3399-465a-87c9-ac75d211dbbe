package com.example.batterystatusapp.data.database

import androidx.room.*
import com.example.batterystatusapp.data.model.BatteryAlertEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for battery alert operations
 */
@Dao
interface BatteryAlertDao {

    /**
     * Insert a new battery alert
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBatteryAlert(batteryAlert: BatteryAlertEntity): Long

    /**
     * Update an existing battery alert
     */
    @Update
    suspend fun updateBatteryAlert(batteryAlert: BatteryAlertEntity)

    /**
     * Delete a battery alert
     */
    @Delete
    suspend fun deleteBatteryAlert(batteryAlert: BatteryAlertEntity)

    /**
     * Delete a battery alert by ID
     */
    @Query("DELETE FROM battery_alerts WHERE id = :alertId")
    suspend fun deleteBatteryAlertById(alertId: Long)

    /**
     * Get all battery alerts
     */
    @Query("SELECT * FROM battery_alerts ORDER BY createdAt DESC")
    fun getAllBatteryAlerts(): Flow<List<BatteryAlertEntity>>

    /**
     * Get enabled battery alerts
     */
    @Query("SELECT * FROM battery_alerts WHERE isEnabled = 1 ORDER BY priority DESC, createdAt DESC")
    fun getEnabledBatteryAlerts(): Flow<List<BatteryAlertEntity>>

    /**
     * Get battery alert by ID
     */
    @Query("SELECT * FROM battery_alerts WHERE id = :alertId")
    suspend fun getBatteryAlertById(alertId: Long): BatteryAlertEntity?

    /**
     * Get battery alerts by type
     */
    @Query("SELECT * FROM battery_alerts WHERE type = :type ORDER BY createdAt DESC")
    fun getBatteryAlertsByType(type: String): Flow<List<BatteryAlertEntity>>

    /**
     * Get enabled alerts by type
     */
    @Query("SELECT * FROM battery_alerts WHERE type = :type AND isEnabled = 1")
    suspend fun getEnabledAlertsByType(type: String): List<BatteryAlertEntity>

    /**
     * Update alert enabled status
     */
    @Query("UPDATE battery_alerts SET isEnabled = :isEnabled WHERE id = :alertId")
    suspend fun updateAlertEnabledStatus(alertId: Long, isEnabled: Boolean)

    /**
     * Update alert last triggered timestamp
     */
    @Query("UPDATE battery_alerts SET lastTriggered = :timestamp WHERE id = :alertId")
    suspend fun updateLastTriggered(alertId: Long, timestamp: Long)

    /**
     * Get alerts that haven't been triggered recently
     */
    @Query("""
        SELECT * FROM battery_alerts 
        WHERE isEnabled = 1 
        AND (lastTriggered IS NULL OR lastTriggered < :sinceTime)
        ORDER BY priority DESC
    """)
    suspend fun getAlertsNotTriggeredSince(sinceTime: Long): List<BatteryAlertEntity>

    /**
     * Get count of enabled alerts
     */
    @Query("SELECT COUNT(*) FROM battery_alerts WHERE isEnabled = 1")
    suspend fun getEnabledAlertsCount(): Int

    /**
     * Get count of alerts by type
     */
    @Query("SELECT COUNT(*) FROM battery_alerts WHERE type = :type")
    suspend fun getAlertsCountByType(type: String): Int

    /**
     * Delete all battery alerts
     */
    @Query("DELETE FROM battery_alerts")
    suspend fun deleteAllBatteryAlerts()

    /**
     * Get alerts by priority
     */
    @Query("SELECT * FROM battery_alerts WHERE priority = :priority AND isEnabled = 1 ORDER BY createdAt DESC")
    fun getAlertsByPriority(priority: String): Flow<List<BatteryAlertEntity>>

    /**
     * Get recently triggered alerts
     */
    @Query("""
        SELECT * FROM battery_alerts 
        WHERE lastTriggered IS NOT NULL 
        AND lastTriggered > :sinceTime 
        ORDER BY lastTriggered DESC
    """)
    fun getRecentlyTriggeredAlerts(sinceTime: Long): Flow<List<BatteryAlertEntity>>

    /**
     * Check if alert exists for type and threshold
     */
    @Query("SELECT COUNT(*) FROM battery_alerts WHERE type = :type AND threshold = :threshold")
    suspend fun alertExistsForTypeAndThreshold(type: String, threshold: Int): Int

    /**
     * Get default alerts that should be created
     */
    @Query("SELECT COUNT(*) FROM battery_alerts")
    suspend fun getTotalAlertsCount(): Int
}
