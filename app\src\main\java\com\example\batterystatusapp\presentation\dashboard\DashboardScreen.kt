package com.example.batterystatusapp.presentation.dashboard

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.batterystatusapp.data.model.BatteryInfo
import com.example.batterystatusapp.data.model.BatteryStatus
import com.example.batterystatusapp.presentation.components.BatteryInfoCard
import com.example.batterystatusapp.presentation.components.ErrorMessage
import com.example.batterystatusapp.presentation.components.LoadingIndicator

/**
 * Dashboard screen showing battery status overview
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen(
    onNavigateToHistory: () -> Unit,
    onNavigateToApps: () -> Unit,
    onNavigateToSettings: () -> Unit,
    viewModel: DashboardViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Battery Status",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(onClick = { viewModel.refreshData() }) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Refresh",
                        modifier = if (uiState.isRefreshing) {
                            Modifier.rotate(
                                animateFloatAsState(
                                    targetValue = 360f,
                                    animationSpec = infiniteRepeatable(
                                        animation = tween(1000, easing = LinearEasing),
                                        repeatMode = RepeatMode.Restart
                                    ),
                                    label = "refresh_rotation"
                                ).value
                            )
                        } else Modifier
                    )
                }
                IconButton(onClick = onNavigateToSettings) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "Settings"
                    )
                }
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        when {
            uiState.isLoading -> {
                LoadingIndicator(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp)
                )
            }
            
            uiState.error != null -> {
                ErrorMessage(
                    message = uiState.error,
                    onRetry = { viewModel.refreshData() },
                    onDismiss = { viewModel.clearError() }
                )
            }
            
            uiState.batteryInfo != null -> {
                BatteryDashboardContent(
                    batteryInfo = uiState.batteryInfo,
                    viewModel = viewModel,
                    onNavigateToHistory = onNavigateToHistory,
                    onNavigateToApps = onNavigateToApps
                )
            }
        }
    }
}

@Composable
private fun BatteryDashboardContent(
    batteryInfo: BatteryInfo,
    viewModel: DashboardViewModel,
    onNavigateToHistory: () -> Unit,
    onNavigateToApps: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Circular Battery Indicator
        CircularBatteryIndicator(
            percentage = batteryInfo.percentage,
            isCharging = batteryInfo.isCharging,
            color = viewModel.getBatteryPercentageColor()
        )

        // Battery Status Text
        Text(
            text = "${batteryInfo.percentage}%",
            style = MaterialTheme.typography.displayLarge,
            fontWeight = FontWeight.Bold,
            color = viewModel.getBatteryPercentageColor()
        )

        Text(
            text = batteryInfo.status.displayName,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Battery Info Cards
        val batteryInfoItems = listOf(
            Triple("Health", batteryInfo.health.displayName, Icons.Default.Favorite),
            Triple("Temperature", viewModel.formatTemperature(batteryInfo.temperature), Icons.Default.Thermostat),
            Triple("Voltage", "${batteryInfo.voltage} mV", Icons.Default.ElectricBolt),
            Triple("Technology", batteryInfo.technology, Icons.Default.Memory)
        )

        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.height(260.dp)
        ) {
            items(batteryInfoItems) { (title, value, icon) ->
                BatteryInfoCard(
                    title = title,
                    value = value,
                    icon = icon,
                    color = when (title) {
                        "Health" -> viewModel.getBatteryHealthColor()
                        "Temperature" -> if (batteryInfo.temperature > 35) Color.Red else Color.Blue
                        "Voltage" -> MaterialTheme.colorScheme.primary
                        else -> MaterialTheme.colorScheme.secondary
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Quick Action Buttons
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedButton(
                onClick = onNavigateToHistory,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.History,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("History")
            }
            
            OutlinedButton(
                onClick = onNavigateToApps,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.Apps,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Apps")
            }
        }
    }
}

@Composable
private fun CircularBatteryIndicator(
    percentage: Int,
    isCharging: Boolean,
    color: Color,
    modifier: Modifier = Modifier
) {
    val animatedPercentage by animateFloatAsState(
        targetValue = percentage.toFloat(),
        animationSpec = tween(durationMillis = 1000, easing = EaseOutCubic),
        label = "battery_percentage"
    )

    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier.size(200.dp)
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawCircularBatteryIndicator(
                percentage = animatedPercentage,
                color = color,
                isCharging = isCharging
            )
        }
        
        if (isCharging) {
            Icon(
                imageVector = Icons.Default.BatteryChargingFull,
                contentDescription = "Charging",
                tint = color,
                modifier = Modifier.size(48.dp)
            )
        }
    }
}

private fun DrawScope.drawCircularBatteryIndicator(
    percentage: Float,
    color: Color,
    isCharging: Boolean
) {
    val strokeWidth = 16.dp.toPx()
    val radius = (size.minDimension - strokeWidth) / 2
    val center = Offset(size.width / 2, size.height / 2)
    
    // Background circle
    drawCircle(
        color = color.copy(alpha = 0.2f),
        radius = radius,
        center = center,
        style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
    )
    
    // Progress arc
    val sweepAngle = (percentage / 100f) * 360f
    drawArc(
        color = color,
        startAngle = -90f,
        sweepAngle = sweepAngle,
        useCenter = false,
        topLeft = Offset(
            center.x - radius,
            center.y - radius
        ),
        size = Size(radius * 2, radius * 2),
        style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
    )
}
