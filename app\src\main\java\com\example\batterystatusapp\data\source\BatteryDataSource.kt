package com.example.batterystatusapp.data.source

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import com.example.batterystatusapp.data.model.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Data source for battery information using Android's BatteryManager
 */
@Singleton
class BatteryDataSource @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private val batteryManager: BatteryManager by lazy {
        context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
    }

    /**
     * Get current battery information
     */
    fun getCurrentBatteryInfo(): BatteryInfo {
        val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        
        return batteryIntent?.let { intent ->
            val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
            val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
            val percentage = if (level != -1 && scale != -1) {
                (level * 100 / scale.toFloat()).toInt()
            } else {
                -1
            }

            val health = intent.getIntExtra(BatteryManager.EXTRA_HEALTH, BatteryManager.BATTERY_HEALTH_UNKNOWN)
            val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, BatteryManager.BATTERY_STATUS_UNKNOWN)
            val temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) / 10.0f // Convert to Celsius
            val voltage = intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0)
            val technology = intent.getStringExtra(BatteryManager.EXTRA_TECHNOLOGY) ?: "Unknown"
            val plugged = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)

            // Get additional battery properties if available (API 21+)
            val capacity = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
            } else {
                percentage
            }

            val current = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            } else {
                0
            }

            val chargeTimeRemaining = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val remaining = batteryManager.computeChargeTimeRemaining()
                if (remaining != -1L) remaining else null
            } else {
                null
            }

            BatteryInfo(
                percentage = percentage,
                health = mapBatteryHealth(health),
                status = mapBatteryStatus(status),
                temperature = temperature,
                voltage = voltage,
                current = current,
                capacity = capacity,
                technology = technology,
                isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING,
                pluggedType = mapPluggedType(plugged),
                chargeTimeRemaining = chargeTimeRemaining
            )
        } ?: run {
            Timber.e("Failed to get battery information")
            BatteryInfo(
                percentage = -1,
                health = BatteryHealth.UNKNOWN,
                status = BatteryStatus.UNKNOWN,
                temperature = 0f,
                voltage = 0,
                current = 0,
                capacity = 0,
                technology = "Unknown",
                isCharging = false,
                pluggedType = PluggedType.NONE,
                chargeTimeRemaining = null
            )
        }
    }

    /**
     * Get battery information as a Flow for real-time updates
     */
    fun getBatteryInfoFlow(): Flow<BatteryInfo> = flow {
        while (true) {
            emit(getCurrentBatteryInfo())
            kotlinx.coroutines.delay(1000) // Update every second
        }
    }

    /**
     * Map Android battery health constants to our enum
     */
    private fun mapBatteryHealth(health: Int): BatteryHealth {
        return when (health) {
            BatteryManager.BATTERY_HEALTH_GOOD -> BatteryHealth.GOOD
            BatteryManager.BATTERY_HEALTH_OVERHEAT -> BatteryHealth.OVERHEAT
            BatteryManager.BATTERY_HEALTH_DEAD -> BatteryHealth.DEAD
            BatteryManager.BATTERY_HEALTH_OVER_VOLTAGE -> BatteryHealth.OVER_VOLTAGE
            BatteryManager.BATTERY_HEALTH_UNSPECIFIED_FAILURE -> BatteryHealth.UNSPECIFIED_FAILURE
            BatteryManager.BATTERY_HEALTH_COLD -> BatteryHealth.COLD
            else -> BatteryHealth.UNKNOWN
        }
    }

    /**
     * Map Android battery status constants to our enum
     */
    private fun mapBatteryStatus(status: Int): BatteryStatus {
        return when (status) {
            BatteryManager.BATTERY_STATUS_CHARGING -> BatteryStatus.CHARGING
            BatteryManager.BATTERY_STATUS_DISCHARGING -> BatteryStatus.DISCHARGING
            BatteryManager.BATTERY_STATUS_NOT_CHARGING -> BatteryStatus.NOT_CHARGING
            BatteryManager.BATTERY_STATUS_FULL -> BatteryStatus.FULL
            else -> BatteryStatus.UNKNOWN
        }
    }

    /**
     * Map Android plugged type constants to our enum
     */
    private fun mapPluggedType(plugged: Int): PluggedType {
        return when (plugged) {
            BatteryManager.BATTERY_PLUGGED_AC -> PluggedType.AC
            BatteryManager.BATTERY_PLUGGED_USB -> PluggedType.USB
            BatteryManager.BATTERY_PLUGGED_WIRELESS -> PluggedType.WIRELESS
            else -> PluggedType.NONE
        }
    }
}
