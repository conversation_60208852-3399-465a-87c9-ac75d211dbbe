package com.example.batterystatusapp.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Data class representing battery alert configuration
 */
data class BatteryAlert(
    val id: Long = 0,
    val type: AlertType,
    val threshold: Int, // percentage or temperature value
    val isEnabled: Boolean,
    val title: String,
    val message: String,
    val priority: AlertPriority = AlertPriority.NORMAL
)

/**
 * Room entity for storing battery alerts
 */
@Entity(tableName = "battery_alerts")
data class BatteryAlertEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val type: String,
    val threshold: Int,
    val isEnabled: Boolean,
    val title: String,
    val message: String,
    val priority: String,
    val createdAt: Long,
    val lastTriggered: Long?
)

/**
 * Alert type enumeration
 */
enum class AlertType(val displayName: String) {
    LOW_BATTERY("Low Battery"),
    HIGH_TEMPERATURE("High Temperature"),
    CHARGING_COMPLETE("Charging Complete"),
    BATTERY_HEALTH("Battery Health"),
    POWER_CONSUMPTION("High Power Consumption")
}

/**
 * Alert priority enumeration
 */
enum class AlertPriority(val displayName: String) {
    LOW("Low"),
    NORMAL("Normal"),
    HIGH("High"),
    CRITICAL("Critical")
}

/**
 * Extension functions for converting between domain models and entities
 */
fun BatteryAlert.toEntity(): BatteryAlertEntity {
    return BatteryAlertEntity(
        id = id,
        type = type.name,
        threshold = threshold,
        isEnabled = isEnabled,
        title = title,
        message = message,
        priority = priority.name,
        createdAt = System.currentTimeMillis(),
        lastTriggered = null
    )
}

fun BatteryAlertEntity.toDomainModel(): BatteryAlert {
    return BatteryAlert(
        id = id,
        type = AlertType.valueOf(type),
        threshold = threshold,
        isEnabled = isEnabled,
        title = title,
        message = message,
        priority = AlertPriority.valueOf(priority)
    )
}
