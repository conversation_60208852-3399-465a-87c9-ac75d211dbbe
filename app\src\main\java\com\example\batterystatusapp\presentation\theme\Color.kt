package com.example.batterystatusapp.presentation.theme

import androidx.compose.ui.graphics.Color

// Battery Status App Color Palette

// Primary Colors - Green theme for battery/energy
val BatteryGreen10 = Color(0xFF003314)
val BatteryGreen20 = Color(0xFF006627)
val BatteryGreen30 = Color(0xFF00993B)
val BatteryGreen40 = Color(0xFF00CC4F)
val BatteryGreen50 = Color(0xFF00FF63)
val BatteryGreen60 = Color(0xFF33FF7F)
val BatteryGreen70 = Color(0xFF66FF9B)
val BatteryGreen80 = Color(0xFF99FFB7)
val BatteryGreen90 = Color(0xFFCCFFD3)
val BatteryGreen95 = Color(0xFFE6FFE9)
val BatteryGreen99 = Color(0xFFF9FFFA)

// Secondary Colors - Blue for technology/digital
val TechBlue10 = Color(0xFF001F33)
val TechBlue20 = Color(0xFF003D66)
val TechBlue30 = Color(0xFF005C99)
val TechBlue40 = Color(0xFF007ACC)
val TechBlue50 = Color(0xFF0099FF)
val TechBlue60 = Color(0xFF33ADFF)
val TechBlue70 = Color(0xFF66C2FF)
val TechBlue80 = Color(0xFF99D6FF)
val TechBlue90 = Color(0xFFCCEBFF)
val TechBlue95 = Color(0xFFE6F5FF)
val TechBlue99 = Color(0xFFF9FCFF)

// Warning Colors - Orange/Red for alerts
val WarningOrange10 = Color(0xFF331A00)
val WarningOrange20 = Color(0xFF663300)
val WarningOrange30 = Color(0xFF994D00)
val WarningOrange40 = Color(0xFFCC6600)
val WarningOrange50 = Color(0xFFFF8000)
val WarningOrange60 = Color(0xFFFF9933)
val WarningOrange70 = Color(0xFFFFB366)
val WarningOrange80 = Color(0xFFFFCC99)
val WarningOrange90 = Color(0xFFFFE6CC)
val WarningOrange95 = Color(0xFFFFF2E6)
val WarningOrange99 = Color(0xFFFFFCF9)

val CriticalRed10 = Color(0xFF330000)
val CriticalRed20 = Color(0xFF660000)
val CriticalRed30 = Color(0xFF990000)
val CriticalRed40 = Color(0xFFCC0000)
val CriticalRed50 = Color(0xFFFF0000)
val CriticalRed60 = Color(0xFFFF3333)
val CriticalRed70 = Color(0xFFFF6666)
val CriticalRed80 = Color(0xFFFF9999)
val CriticalRed90 = Color(0xFFFFCCCC)
val CriticalRed95 = Color(0xFFFFE6E6)
val CriticalRed99 = Color(0xFFFFF9F9)

// Neutral Colors
val Neutral10 = Color(0xFF1A1C1E)
val Neutral20 = Color(0xFF2F3033)
val Neutral30 = Color(0xFF454649)
val Neutral40 = Color(0xFF5C5D60)
val Neutral50 = Color(0xFF747578)
val Neutral60 = Color(0xFF8E8E91)
val Neutral70 = Color(0xFFA9A9AC)
val Neutral80 = Color(0xFFC4C4C7)
val Neutral90 = Color(0xFFE0E0E3)
val Neutral95 = Color(0xFFF0F0F2)
val Neutral99 = Color(0xFFFCFCFC)

val NeutralVariant10 = Color(0xFF191C1B)
val NeutralVariant20 = Color(0xFF2E312F)
val NeutralVariant30 = Color(0xFF444745)
val NeutralVariant40 = Color(0xFF5C5F5C)
val NeutralVariant50 = Color(0xFF747874)
val NeutralVariant60 = Color(0xFF8E918D)
val NeutralVariant70 = Color(0xFFA9ACA8)
val NeutralVariant80 = Color(0xFFC4C7C3)
val NeutralVariant90 = Color(0xFFE0E3DF)
val NeutralVariant95 = Color(0xFFF0F2EE)
val NeutralVariant99 = Color(0xFFFCFDF9)

// Battery Level Colors
val BatteryLevelCritical = CriticalRed50
val BatteryLevelLow = WarningOrange50
val BatteryLevelMedium = WarningOrange30
val BatteryLevelGood = BatteryGreen40
val BatteryLevelExcellent = BatteryGreen50

// Temperature Colors
val TemperatureCold = TechBlue50
val TemperatureNormal = BatteryGreen40
val TemperatureWarm = WarningOrange40
val TemperatureHot = CriticalRed50

// Health Status Colors
val HealthGood = BatteryGreen50
val HealthWarning = WarningOrange50
val HealthCritical = CriticalRed50
val HealthUnknown = Neutral50
