package com.example.batterystatusapp.di

import android.content.Context
import androidx.room.Room
import com.example.batterystatusapp.data.database.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for database dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideBatteryDatabase(@ApplicationContext context: Context): BatteryDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            BatteryDatabase::class.java,
            "battery_database"
        )
            .fallbackToDestructiveMigration() // For development - remove in production
            .build()
    }

    @Provides
    fun provideBatteryDao(database: BatteryDatabase): BatteryDao {
        return database.batteryDao()
    }

    @Provides
    fun provideAppPowerUsageDao(database: BatteryDatabase): AppPowerUsageDao {
        return database.appPowerUsageDao()
    }

    @Provides
    fun provideBatteryAlertDao(database: BatteryDatabase): BatteryAlertDao {
        return database.batteryAlertDao()
    }

    @Provides
    fun provideUserPreferencesDao(database: BatteryDatabase): UserPreferencesDao {
        return database.userPreferencesDao()
    }
}
