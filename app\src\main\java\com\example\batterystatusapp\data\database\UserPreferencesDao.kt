package com.example.batterystatusapp.data.database

import androidx.room.*
import com.example.batterystatusapp.data.model.UserPreferencesEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for user preferences operations
 */
@Dao
interface UserPreferencesDao {

    /**
     * Insert or update user preferences
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserPreferences(userPreferences: UserPreferencesEntity)

    /**
     * Update user preferences
     */
    @Update
    suspend fun updateUserPreferences(userPreferences: UserPreferencesEntity)

    /**
     * Get user preferences
     */
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    fun getUserPreferences(): Flow<UserPreferencesEntity?>

    /**
     * Get user preferences synchronously
     */
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    suspend fun getUserPreferencesSync(): UserPreferencesEntity?

    /**
     * Update theme preference
     */
    @Query("UPDATE user_preferences SET theme = :theme, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateTheme(theme: String, timestamp: Long)

    /**
     * Update dynamic color preference
     */
    @Query("UPDATE user_preferences SET isDynamicColorEnabled = :isEnabled, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateDynamicColor(isEnabled: Boolean, timestamp: Long)

    /**
     * Update notifications preference
     */
    @Query("UPDATE user_preferences SET isNotificationsEnabled = :isEnabled, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateNotifications(isEnabled: Boolean, timestamp: Long)

    /**
     * Update data collection interval
     */
    @Query("UPDATE user_preferences SET dataCollectionInterval = :interval, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateDataCollectionInterval(interval: String, timestamp: Long)

    /**
     * Update background monitoring preference
     */
    @Query("UPDATE user_preferences SET isBackgroundMonitoringEnabled = :isEnabled, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateBackgroundMonitoring(isEnabled: Boolean, timestamp: Long)

    /**
     * Update temperature unit preference
     */
    @Query("UPDATE user_preferences SET temperatureUnit = :unit, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateTemperatureUnit(unit: String, timestamp: Long)

    /**
     * Update chart time range preference
     */
    @Query("UPDATE user_preferences SET chartTimeRange = :range, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateChartTimeRange(range: String, timestamp: Long)

    /**
     * Update high contrast preference
     */
    @Query("UPDATE user_preferences SET isHighContrastEnabled = :isEnabled, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateHighContrast(isEnabled: Boolean, timestamp: Long)

    /**
     * Update reduced animations preference
     */
    @Query("UPDATE user_preferences SET isReducedAnimationsEnabled = :isEnabled, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateReducedAnimations(isEnabled: Boolean, timestamp: Long)

    /**
     * Check if preferences exist
     */
    @Query("SELECT COUNT(*) FROM user_preferences WHERE id = 1")
    suspend fun preferencesExist(): Int

    /**
     * Delete all preferences (for reset)
     */
    @Query("DELETE FROM user_preferences")
    suspend fun deleteAllPreferences()

    /**
     * Get last updated timestamp
     */
    @Query("SELECT lastUpdated FROM user_preferences WHERE id = 1")
    suspend fun getLastUpdated(): Long?
}
