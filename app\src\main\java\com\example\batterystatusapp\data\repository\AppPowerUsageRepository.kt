package com.example.batterystatusapp.data.repository

import com.example.batterystatusapp.data.database.AppPowerUsageDao
import com.example.batterystatusapp.data.database.PowerUsageStatistics
import com.example.batterystatusapp.data.model.AppPowerUsage
import com.example.batterystatusapp.data.model.toDomainModel
import com.example.batterystatusapp.data.model.toEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for app power usage operations
 */
@Singleton
class AppPowerUsageRepository @Inject constructor(
    private val appPowerUsageDao: AppPowerUsageDao
) {

    /**
     * Get all app power usage data
     */
    fun getAllAppPowerUsage(): Flow<List<AppPowerUsage>> {
        return appPowerUsageDao.getAllAppPowerUsage().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get top power consuming apps
     */
    fun getTopPowerConsumingApps(limit: Int = 10): Flow<List<AppPowerUsage>> {
        return appPowerUsageDao.getTopPowerConsumingApps(limit).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Save app power usage data
     */
    suspend fun saveAppPowerUsage(appPowerUsage: AppPowerUsage) {
        try {
            appPowerUsageDao.insertAppPowerUsage(appPowerUsage.toEntity())
            Timber.d("App power usage saved for: ${appPowerUsage.appName}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save app power usage for: ${appPowerUsage.appName}")
        }
    }

    /**
     * Save multiple app power usage entries
     */
    suspend fun saveAppPowerUsageList(appPowerUsageList: List<AppPowerUsage>) {
        try {
            val entities = appPowerUsageList.map { it.toEntity() }
            appPowerUsageDao.insertAppPowerUsageList(entities)
            Timber.d("Saved ${appPowerUsageList.size} app power usage entries")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save app power usage list")
        }
    }

    /**
     * Get power usage for specific app
     */
    suspend fun getAppPowerUsage(packageName: String): AppPowerUsage? {
        return try {
            appPowerUsageDao.getAppPowerUsage(packageName)?.toDomainModel()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get app power usage for: $packageName")
            null
        }
    }

    /**
     * Get apps with high background usage
     */
    fun getAppsWithHighBackgroundUsage(minUsage: Double = 1.0): Flow<List<AppPowerUsage>> {
        return appPowerUsageDao.getAppsWithHighBackgroundUsage(minUsage).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Search apps by name
     */
    fun searchAppsByName(searchQuery: String): Flow<List<AppPowerUsage>> {
        return appPowerUsageDao.searchAppsByName(searchQuery).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get apps above power usage threshold
     */
    fun getAppsAboveThreshold(threshold: Double): Flow<List<AppPowerUsage>> {
        return appPowerUsageDao.getAppsAboveThreshold(threshold).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    /**
     * Get power usage statistics
     */
    suspend fun getPowerUsageStatistics(): PowerUsageStatistics? {
        return try {
            appPowerUsageDao.getPowerUsageStatistics()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get power usage statistics")
            null
        }
    }

    /**
     * Get total power usage
     */
    suspend fun getTotalPowerUsage(): Double {
        return try {
            appPowerUsageDao.getTotalPowerUsage() ?: 0.0
        } catch (e: Exception) {
            Timber.e(e, "Failed to get total power usage")
            0.0
        }
    }

    /**
     * Get tracked apps count
     */
    suspend fun getTrackedAppsCount(): Int {
        return try {
            appPowerUsageDao.getTrackedAppsCount()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get tracked apps count")
            0
        }
    }

    /**
     * Delete app power usage data
     */
    suspend fun deleteAppPowerUsage(packageName: String) {
        try {
            appPowerUsageDao.deleteAppPowerUsage(packageName)
            Timber.d("Deleted app power usage for: $packageName")
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete app power usage for: $packageName")
        }
    }

    /**
     * Clean up old app power usage data
     */
    suspend fun cleanupOldData(olderThanDays: Int = 7) {
        try {
            val cutoffTime = System.currentTimeMillis() - (olderThanDays * 24 * 60 * 60 * 1000L)
            appPowerUsageDao.deleteAppsUpdatedBefore(cutoffTime)
            Timber.d("Cleaned up app power usage data older than $olderThanDays days")
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup old app power usage data")
        }
    }

    /**
     * Delete all app power usage data
     */
    suspend fun deleteAllAppPowerUsage() {
        try {
            appPowerUsageDao.deleteAllAppPowerUsage()
            Timber.d("All app power usage data deleted")
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete all app power usage data")
        }
    }
}
