package com.example.batterystatusapp.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Data class representing user preferences
 */
data class UserPreferences(
    val theme: AppTheme = AppTheme.SYSTEM,
    val isDynamicColorEnabled: <PERSON>olean = true,
    val isNotificationsEnabled: Boolean = true,
    val dataCollectionInterval: DataCollectionInterval = DataCollectionInterval.NORMAL,
    val isBackgroundMonitoringEnabled: Boolean = true,
    val temperatureUnit: TemperatureUnit = TemperatureUnit.CELSIUS,
    val chartTimeRange: ChartTimeRange = ChartTimeRange.LAST_24_HOURS,
    val isHighContrastEnabled: Boolean = false,
    val isReducedAnimationsEnabled: Boolean = false
)

/**
 * Room entity for storing user preferences
 */
@Entity(tableName = "user_preferences")
data class UserPreferencesEntity(
    @PrimaryKey
    val id: Int = 1, // Single row for preferences
    val theme: String,
    val isDynamicColorEnabled: Boolean,
    val isNotificationsEnabled: Boolean,
    val dataCollectionInterval: String,
    val isBackgroundMonitoringEnabled: Boolean,
    val temperatureUnit: String,
    val chartTimeRange: String,
    val isHighContrastEnabled: Boolean,
    val isReducedAnimationsEnabled: Boolean,
    val lastUpdated: Long
)

/**
 * App theme enumeration
 */
enum class AppTheme(val displayName: String) {
    LIGHT("Light"),
    DARK("Dark"),
    SYSTEM("Follow System")
}

/**
 * Data collection interval enumeration
 */
enum class DataCollectionInterval(val displayName: String, val intervalMs: Long) {
    FAST("Fast (30s)", 30_000L),
    NORMAL("Normal (1m)", 60_000L),
    SLOW("Slow (5m)", 300_000L),
    VERY_SLOW("Very Slow (15m)", 900_000L)
}

/**
 * Temperature unit enumeration
 */
enum class TemperatureUnit(val displayName: String, val symbol: String) {
    CELSIUS("Celsius", "°C"),
    FAHRENHEIT("Fahrenheit", "°F")
}

/**
 * Chart time range enumeration
 */
enum class ChartTimeRange(val displayName: String, val hours: Int) {
    LAST_HOUR("Last Hour", 1),
    LAST_6_HOURS("Last 6 Hours", 6),
    LAST_24_HOURS("Last 24 Hours", 24),
    LAST_WEEK("Last Week", 168),
    LAST_MONTH("Last Month", 720)
}

/**
 * Extension functions for converting between domain models and entities
 */
fun UserPreferences.toEntity(): UserPreferencesEntity {
    return UserPreferencesEntity(
        theme = theme.name,
        isDynamicColorEnabled = isDynamicColorEnabled,
        isNotificationsEnabled = isNotificationsEnabled,
        dataCollectionInterval = dataCollectionInterval.name,
        isBackgroundMonitoringEnabled = isBackgroundMonitoringEnabled,
        temperatureUnit = temperatureUnit.name,
        chartTimeRange = chartTimeRange.name,
        isHighContrastEnabled = isHighContrastEnabled,
        isReducedAnimationsEnabled = isReducedAnimationsEnabled,
        lastUpdated = System.currentTimeMillis()
    )
}

fun UserPreferencesEntity.toDomainModel(): UserPreferences {
    return UserPreferences(
        theme = AppTheme.valueOf(theme),
        isDynamicColorEnabled = isDynamicColorEnabled,
        isNotificationsEnabled = isNotificationsEnabled,
        dataCollectionInterval = DataCollectionInterval.valueOf(dataCollectionInterval),
        isBackgroundMonitoringEnabled = isBackgroundMonitoringEnabled,
        temperatureUnit = TemperatureUnit.valueOf(temperatureUnit),
        chartTimeRange = ChartTimeRange.valueOf(chartTimeRange),
        isHighContrastEnabled = isHighContrastEnabled,
        isReducedAnimationsEnabled = isReducedAnimationsEnabled
    )
}
