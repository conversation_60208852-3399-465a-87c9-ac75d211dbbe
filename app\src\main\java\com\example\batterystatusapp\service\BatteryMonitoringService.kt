package com.example.batterystatusapp.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.example.batterystatusapp.MainActivity
import com.example.batterystatusapp.R
import com.example.batterystatusapp.data.repository.BatteryRepository
import com.example.batterystatusapp.data.repository.UserPreferencesRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.first
import timber.log.Timber
import javax.inject.Inject

/**
 * Foreground service for continuous battery monitoring
 */
@AndroidEntryPoint
class BatteryMonitoringService : Service() {

    @Inject
    lateinit var batteryRepository: BatteryRepository
    
    @Inject
    lateinit var userPreferencesRepository: UserPreferencesRepository

    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var monitoringJob: Job? = null

    companion object {
        const val CHANNEL_ID = "battery_monitoring_channel"
        const val NOTIFICATION_ID = 1001
        const val ACTION_START_MONITORING = "START_MONITORING"
        const val ACTION_STOP_MONITORING = "STOP_MONITORING"
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        Timber.d("Battery monitoring service created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_MONITORING -> startMonitoring()
            ACTION_STOP_MONITORING -> stopMonitoring()
            else -> startMonitoring()
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        stopMonitoring()
        serviceScope.cancel()
        Timber.d("Battery monitoring service destroyed")
    }

    /**
     * Start battery monitoring
     */
    private fun startMonitoring() {
        if (monitoringJob?.isActive == true) {
            Timber.d("Battery monitoring already active")
            return
        }

        val notification = createForegroundNotification()
        startForeground(NOTIFICATION_ID, notification)

        monitoringJob = serviceScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferences().first()
                val interval = preferences.dataCollectionInterval.intervalMs

                while (isActive) {
                    try {
                        val batteryInfo = batteryRepository.getCurrentBatteryInfo()
                        batteryRepository.saveBatteryInfo(batteryInfo)
                        
                        // Update notification with current battery level
                        updateNotification(batteryInfo.percentage)
                        
                        Timber.d("Battery data collected: ${batteryInfo.percentage}%")
                    } catch (e: Exception) {
                        Timber.e(e, "Error collecting battery data")
                    }

                    delay(interval)
                }
            } catch (e: Exception) {
                Timber.e(e, "Battery monitoring error")
            }
        }

        Timber.d("Battery monitoring started")
    }

    /**
     * Stop battery monitoring
     */
    private fun stopMonitoring() {
        monitoringJob?.cancel()
        monitoringJob = null
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
        Timber.d("Battery monitoring stopped")
    }

    /**
     * Create notification channel for Android O+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Battery Monitoring",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Continuous battery monitoring service"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Create foreground notification
     */
    private fun createForegroundNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Battery Monitoring Active")
            .setContentText("Collecting battery data...")
            .setSmallIcon(R.drawable.ic_battery_monitoring) // You'll need to add this icon
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()
    }

    /**
     * Update notification with current battery percentage
     */
    private fun updateNotification(batteryPercentage: Int) {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Battery: $batteryPercentage%")
            .setContentText("Monitoring battery status")
            .setSmallIcon(R.drawable.ic_battery_monitoring)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}
