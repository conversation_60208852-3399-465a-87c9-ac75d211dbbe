package com.example.batterystatusapp.data.repository

import com.example.batterystatusapp.data.database.UserPreferencesDao
import com.example.batterystatusapp.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for user preferences operations
 */
@Singleton
class UserPreferencesRepository @Inject constructor(
    private val userPreferencesDao: UserPreferencesDao
) {

    /**
     * Get user preferences as Flow
     */
    fun getUserPreferences(): Flow<UserPreferences> {
        return userPreferencesDao.getUserPreferences().map { entity ->
            entity?.toDomainModel() ?: getDefaultPreferences()
        }
    }

    /**
     * Get user preferences synchronously
     */
    suspend fun getUserPreferencesSync(): UserPreferences {
        return try {
            userPreferencesDao.getUserPreferencesSync()?.toDomainModel() ?: getDefaultPreferences()
        } catch (e: Exception) {
            Timber.e(e, "Failed to get user preferences")
            getDefaultPreferences()
        }
    }

    /**
     * Save user preferences
     */
    suspend fun saveUserPreferences(preferences: UserPreferences) {
        try {
            userPreferencesDao.insertUserPreferences(preferences.toEntity())
            Timber.d("User preferences saved")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save user preferences")
        }
    }

    /**
     * Update theme preference
     */
    suspend fun updateTheme(theme: AppTheme) {
        try {
            userPreferencesDao.updateTheme(theme.name, System.currentTimeMillis())
            Timber.d("Theme updated to: ${theme.displayName}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update theme")
        }
    }

    /**
     * Update dynamic color preference
     */
    suspend fun updateDynamicColor(isEnabled: Boolean) {
        try {
            userPreferencesDao.updateDynamicColor(isEnabled, System.currentTimeMillis())
            Timber.d("Dynamic color updated: $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update dynamic color")
        }
    }

    /**
     * Update notifications preference
     */
    suspend fun updateNotifications(isEnabled: Boolean) {
        try {
            userPreferencesDao.updateNotifications(isEnabled, System.currentTimeMillis())
            Timber.d("Notifications updated: $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update notifications")
        }
    }

    /**
     * Update data collection interval
     */
    suspend fun updateDataCollectionInterval(interval: DataCollectionInterval) {
        try {
            userPreferencesDao.updateDataCollectionInterval(interval.name, System.currentTimeMillis())
            Timber.d("Data collection interval updated: ${interval.displayName}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update data collection interval")
        }
    }

    /**
     * Update background monitoring preference
     */
    suspend fun updateBackgroundMonitoring(isEnabled: Boolean) {
        try {
            userPreferencesDao.updateBackgroundMonitoring(isEnabled, System.currentTimeMillis())
            Timber.d("Background monitoring updated: $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update background monitoring")
        }
    }

    /**
     * Update temperature unit preference
     */
    suspend fun updateTemperatureUnit(unit: TemperatureUnit) {
        try {
            userPreferencesDao.updateTemperatureUnit(unit.name, System.currentTimeMillis())
            Timber.d("Temperature unit updated: ${unit.displayName}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update temperature unit")
        }
    }

    /**
     * Update chart time range preference
     */
    suspend fun updateChartTimeRange(range: ChartTimeRange) {
        try {
            userPreferencesDao.updateChartTimeRange(range.name, System.currentTimeMillis())
            Timber.d("Chart time range updated: ${range.displayName}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update chart time range")
        }
    }

    /**
     * Update high contrast preference
     */
    suspend fun updateHighContrast(isEnabled: Boolean) {
        try {
            userPreferencesDao.updateHighContrast(isEnabled, System.currentTimeMillis())
            Timber.d("High contrast updated: $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update high contrast")
        }
    }

    /**
     * Update reduced animations preference
     */
    suspend fun updateReducedAnimations(isEnabled: Boolean) {
        try {
            userPreferencesDao.updateReducedAnimations(isEnabled, System.currentTimeMillis())
            Timber.d("Reduced animations updated: $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "Failed to update reduced animations")
        }
    }

    /**
     * Initialize default preferences if they don't exist
     */
    suspend fun initializeDefaultPreferences() {
        try {
            val exists = userPreferencesDao.preferencesExist() > 0
            if (!exists) {
                saveUserPreferences(getDefaultPreferences())
                Timber.d("Default preferences initialized")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize default preferences")
        }
    }

    /**
     * Reset preferences to default
     */
    suspend fun resetToDefaults() {
        try {
            userPreferencesDao.deleteAllPreferences()
            saveUserPreferences(getDefaultPreferences())
            Timber.d("Preferences reset to defaults")
        } catch (e: Exception) {
            Timber.e(e, "Failed to reset preferences")
        }
    }

    /**
     * Get default user preferences
     */
    private fun getDefaultPreferences(): UserPreferences {
        return UserPreferences()
    }
}
