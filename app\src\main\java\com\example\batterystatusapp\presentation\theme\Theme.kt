package com.example.batterystatusapp.presentation.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.batterystatusapp.data.model.AppTheme
import com.example.batterystatusapp.presentation.theme.viewmodel.ThemeViewModel

/**
 * Light color scheme for Battery Status App
 */
private val LightColorScheme = lightColorScheme(
    primary = BatteryGreen40,
    onPrimary = Neutral99,
    primaryContainer = BatteryGreen90,
    onPrimaryContainer = BatteryGreen10,
    secondary = TechBlue40,
    onSecondary = Neutral99,
    secondaryContainer = TechBlue90,
    onSecondaryContainer = TechBlue10,
    tertiary = WarningOrange40,
    onTertiary = Neutral99,
    tertiaryContainer = WarningOrange90,
    onTertiaryContainer = WarningOrange10,
    error = CriticalRed40,
    onError = Neutral99,
    errorContainer = CriticalRed90,
    onErrorContainer = CriticalRed10,
    background = Neutral99,
    onBackground = Neutral10,
    surface = Neutral99,
    onSurface = Neutral10,
    surfaceVariant = NeutralVariant90,
    onSurfaceVariant = NeutralVariant30,
    outline = NeutralVariant50,
    outlineVariant = NeutralVariant80,
    scrim = Neutral10,
    inverseSurface = Neutral20,
    inverseOnSurface = Neutral95,
    inversePrimary = BatteryGreen80,
    surfaceDim = Neutral87,
    surfaceBright = Neutral98,
    surfaceContainerLowest = Neutral100,
    surfaceContainerLow = Neutral96,
    surfaceContainer = Neutral94,
    surfaceContainerHigh = Neutral92,
    surfaceContainerHighest = Neutral90
)

/**
 * Dark color scheme for Battery Status App
 */
private val DarkColorScheme = darkColorScheme(
    primary = BatteryGreen80,
    onPrimary = BatteryGreen20,
    primaryContainer = BatteryGreen30,
    onPrimaryContainer = BatteryGreen90,
    secondary = TechBlue80,
    onSecondary = TechBlue20,
    secondaryContainer = TechBlue30,
    onSecondaryContainer = TechBlue90,
    tertiary = WarningOrange80,
    onTertiary = WarningOrange20,
    tertiaryContainer = WarningOrange30,
    onTertiaryContainer = WarningOrange90,
    error = CriticalRed80,
    onError = CriticalRed20,
    errorContainer = CriticalRed30,
    onErrorContainer = CriticalRed90,
    background = Neutral10,
    onBackground = Neutral90,
    surface = Neutral10,
    onSurface = Neutral90,
    surfaceVariant = NeutralVariant30,
    onSurfaceVariant = NeutralVariant80,
    outline = NeutralVariant60,
    outlineVariant = NeutralVariant30,
    scrim = Neutral10,
    inverseSurface = Neutral90,
    inverseOnSurface = Neutral20,
    inversePrimary = BatteryGreen40,
    surfaceDim = Neutral6,
    surfaceBright = Neutral24,
    surfaceContainerLowest = Neutral4,
    surfaceContainerLow = Neutral10,
    surfaceContainer = Neutral12,
    surfaceContainerHigh = Neutral17,
    surfaceContainerHighest = Neutral22
)

/**
 * High contrast light color scheme
 */
private val HighContrastLightColorScheme = lightColorScheme(
    primary = BatteryGreen30,
    onPrimary = Neutral99,
    primaryContainer = BatteryGreen40,
    onPrimaryContainer = Neutral99,
    secondary = TechBlue30,
    onSecondary = Neutral99,
    secondaryContainer = TechBlue40,
    onSecondaryContainer = Neutral99,
    tertiary = WarningOrange30,
    onTertiary = Neutral99,
    tertiaryContainer = WarningOrange40,
    onTertiaryContainer = Neutral99,
    error = CriticalRed30,
    onError = Neutral99,
    errorContainer = CriticalRed40,
    onErrorContainer = Neutral99,
    background = Neutral99,
    onBackground = Neutral10,
    surface = Neutral99,
    onSurface = Neutral10,
    surfaceVariant = NeutralVariant90,
    onSurfaceVariant = NeutralVariant20,
    outline = NeutralVariant40,
    outlineVariant = NeutralVariant70
)

/**
 * High contrast dark color scheme
 */
private val HighContrastDarkColorScheme = darkColorScheme(
    primary = BatteryGreen90,
    onPrimary = BatteryGreen10,
    primaryContainer = BatteryGreen80,
    onPrimaryContainer = BatteryGreen10,
    secondary = TechBlue90,
    onSecondary = TechBlue10,
    secondaryContainer = TechBlue80,
    onSecondaryContainer = TechBlue10,
    tertiary = WarningOrange90,
    onTertiary = WarningOrange10,
    tertiaryContainer = WarningOrange80,
    onTertiaryContainer = WarningOrange10,
    error = CriticalRed90,
    onError = CriticalRed10,
    errorContainer = CriticalRed80,
    onErrorContainer = CriticalRed10,
    background = Neutral10,
    onBackground = Neutral99,
    surface = Neutral10,
    onSurface = Neutral99,
    surfaceVariant = NeutralVariant30,
    onSurfaceVariant = NeutralVariant90,
    outline = NeutralVariant70,
    outlineVariant = NeutralVariant40
)

/**
 * Main theme composable for Battery Status App
 */
@Composable
fun BatteryStatusAppTheme(
    themeViewModel: ThemeViewModel = hiltViewModel(),
    content: @Composable () -> Unit
) {
    val userPreferences by themeViewModel.userPreferences.collectAsStateWithLifecycle()
    val context = LocalContext.current
    
    val darkTheme = when (userPreferences.theme) {
        AppTheme.LIGHT -> false
        AppTheme.DARK -> true
        AppTheme.SYSTEM -> isSystemInDarkTheme()
    }
    
    val dynamicColor = userPreferences.isDynamicColorEnabled && 
                      Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    
    val highContrast = userPreferences.isHighContrastEnabled
    
    val colorScheme = when {
        dynamicColor -> {
            if (darkTheme) {
                dynamicDarkColorScheme(context)
            } else {
                dynamicLightColorScheme(context)
            }
        }
        highContrast -> {
            if (darkTheme) HighContrastDarkColorScheme else HighContrastLightColorScheme
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }
    
    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}

// Additional color values for missing constants
private val Neutral87 = Color(0xFFDDDDDD)
private val Neutral98 = Color(0xFFFAFAFA)
private val Neutral100 = Color(0xFFFFFFFF)
private val Neutral96 = Color(0xFFF5F5F5)
private val Neutral94 = Color(0xFFF0F0F0)
private val Neutral92 = Color(0xFFEBEBEB)
private val Neutral90 = Color(0xFFE6E6E6)
private val Neutral6 = Color(0xFF0F0F0F)
private val Neutral24 = Color(0xFF3D3D3D)
private val Neutral4 = Color(0xFF0A0A0A)
private val Neutral12 = Color(0xFF1F1F1F)
private val Neutral17 = Color(0xFF2B2B2B)
private val Neutral22 = Color(0xFF383838)
